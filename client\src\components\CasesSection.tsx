import { motion } from 'framer-motion';
import { Car, Store, School } from 'lucide-react';

const CasesSection = () => {
  const cases = [
    {
      image: "https://images.unsplash.com/photo-1678652197831-2f274e193485?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      icon: <Car className="h-6 w-6 text-white" />,
      title: "Motorista de App com Carro Elétrico",
      description: "Migrou para tarifa mais econômica sem nenhum investimento inicial e reduziu custos para carregar seu veículo",
      savings: "20%",
      period: "de redução na conta de luz",
      testimonial: "Quando comecei a usar carro elétrico para trabalhar como motorista de app, minha conta de energia disparou. A Free Energy me conectou com uma solução parceira que ofereceu tarifa mais econômica sem precisar instalar nada. Foi só migrar e economizar.",
      author: "<PERSON>, Motorista de App (Grupo B)",
      buttonText: "Migrar para solução mais econômica",
      linkTo: "https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
    },
    {
      image: "https://images.unsplash.com/photo-1556742111-a301076d9d18?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      icon: <Store className="h-6 w-6 text-white" />,
      title: "Padaria do Seu Carlos",
      description: "Migrou para uma solução parceira mais econômica sem modificar seus equipamentos ou instalações.",
      savings: "R$ 1.200",
      period: "economizados por ano",
      testimonial: "Nunca imaginei que poderia economizar tanto apenas migrando para outra solução, sem investir nada. A Free Energy me educou sobre as possibilidades e me conectou com a solução parceira ideal para meu tipo de negócio.",
      author: "Carlos Silva, Proprietário (Grupo B)",
      buttonText: "Conhecer solução para pequenos negócios",
      linkTo: "https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
    },
    {
      image: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      icon: <School className="h-6 w-6 text-white" />,
      title: "Escola Novo Saber",
      description: "Instituição de grande porte que reduziu significativamente seus custos com migração simples.",
      savings: "35%",
      period: "de redução em custos energéticos",
      testimonial: "Nossa instituição conseguiu reduzir drasticamente os custos com energia através de uma simples migração para uma solução parceira indicada pela Free Energy. Sem investimento inicial e com economia imediata que foi direcionada para melhorias pedagógicas.",
      author: "Ana Paula Mendes, Diretora (Grupo A)",
      buttonText: "Conhecer solução para grandes consumidores",
      linkTo: "https://www.leadenergy.com.br/simular?ch=0f0547d3-7e73-442e-a915-34ef39efe74b&pr=a048c6c3-6224-43ab-ad95-7727cf36d2a2"
    }
  ];

  return (
    <section id="cases" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4">
            Histórias de <span className="text-[#2ECC71]">Sucesso</span> com nossas soluções parceiras
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            Conheça pessoas e empresas que foram conectadas às soluções parceiras ideais através da Free Energy
            e conseguiram economia significativa sem investimento inicial.
          </p>
          <p className="text-sm text-gray-500 mt-2 max-w-2xl mx-auto">
            Importante: A Free Energy atua apenas como intermediadora, educando e conectando clientes às 
            melhores soluções. A implementação e resultados são de responsabilidade dos parceiros.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {cases.map((item, index) => (
            <motion.div 
              key={index}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
            >
              <div className="h-48 overflow-hidden relative">
                <img 
                  src={item.image} 
                  alt={item.title} 
                  className="w-full h-full object-cover object-center transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute top-4 right-4 w-10 h-10 rounded-full flex items-center justify-center" style={{backgroundColor: "#2ECC71"}}>
                  {item.icon}
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold font-montserrat mb-3">{item.title}</h3>
                <p className="text-gray-600 mb-4">
                  {item.description}
                </p>
                <div className="mb-5 flex items-center">
                  <span className="text-2xl font-bold text-[#2ECC71]">{item.savings}</span>
                  <span className="ml-2 text-gray-600">{item.period}</span>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg mb-5">
                  <p className="italic text-gray-600">
                    "{item.testimonial}"
                  </p>
                  <p className="font-medium mt-2">- {item.author}</p>
                </div>
                <a
                  href={item.linkTo}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full bg-[#2196F3] hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 inline-block text-center"
                >
                  {item.buttonText}
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CasesSection;