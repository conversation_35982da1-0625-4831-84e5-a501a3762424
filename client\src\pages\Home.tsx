import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import StorytellingSection from "@/components/StorytellingSection";
import HowItWorksSection from "@/components/HowItWorksSection";
import SolutionsSection from "@/components/SolutionsSection";
import SimulatorSection from "@/components/SimulatorSection";
import CasesSection from "@/components/CasesSection";
import DifferentialsSection from "@/components/DifferentialsSection";
import PreLaunchSection from "@/components/PreLaunchSection";
import Footer from "@/components/Footer";

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="pt-16"> {/* Adiciona espaço para o header fixo */}
        <HeroSection />
        <StorytellingSection />
        <HowItWorksSection />
        <SolutionsSection />
        <SimulatorSection />
        <CasesSection />
        <DifferentialsSection />
        <PreLaunchSection />
        <Footer />
      </div>
    </div>
  );
}