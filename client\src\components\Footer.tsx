import { Send, Instagram, Linkedin, Facebook, ArrowRight } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-neutral-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="text-2xl font-bold font-montserrat mb-4">
              <span className="text-[#2ECC71]">Free</span><span className="text-[#FFC107]">Energy</span>
            </div>
            <p className="text-gray-400 mb-4">
              Conectando você às melhores soluções de economia energética do Brasil.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Soluções</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Para Residências</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Para Comércios</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Para Indústrias</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Energia Solar</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Empresa</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Sobre Nós</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Blog</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Parceiros</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contato</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Fale Conosco</h3>
            <p className="text-gray-400 mb-4">
              Fique por dentro das novidades em energia limpa e sustentável. Entre em contato direto com nossa equipe.
            </p>
            <div>
              <a 
                href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-[#2ECC71] hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300"
              >
                Entrar em contato <ArrowRight className="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
        
        <div className="mt-12 pt-6 border-t border-gray-800 text-center text-gray-500">
          <div className="mb-4">
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">Termos de Uso</a>
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">Política de Privacidade</a>
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">FAQ</a>
          </div>
          <p>&copy; {new Date().getFullYear()} Free Energy. Todos os direitos reservados.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;