import { motion } from 'framer-motion';
import { Search, Users, Battery, ArrowRight } from 'lucide-react';

const HowItWorksSection = () => {
  const steps = [
    {
      icon: <Search className="h-8 w-8 text-white" />,
      title: "Educamos sobre suas opções",
      description: "Identificamos seu perfil de consumo (Grupo A ou B) e apresentamos as possibilidades de economia através de migração simples.",
      bgColor: "#2ECC71",
      number: "01"
    },
    {
      icon: <Users className="h-8 w-8 text-white" />,
      title: "Direcionamos à solução parceira ideal",
      description: "Conectamos você diretamente ao parceiro especializado mais adequado ao seu perfil, sem intermediários adicionais.",
      bgColor: "#FFC107",
      number: "02"
    },
    {
      icon: <Battery className="h-8 w-8 text-white" />,
      title: "Você economiza sem investir",
      description: "Migre para uma solução mais econômica sem investimento inicial e comece a economizar imediatamente na sua conta de energia.",
      bgColor: "#3498DB",
      number: "03"
    }
  ];

  return (
    <section id="como-funciona" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4">
            Como <span className="text-[#2ECC71]">funciona</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Nosso processo é simples e transparente. Educamos e conectamos você com as melhores soluções 
            parceiras para atender às suas necessidades energéticas sem investimento inicial.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {steps.map((step, index) => (
            <motion.div 
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg relative overflow-hidden group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              whileHover={{ y: -10 }}
            >
              <div 
                className="absolute -right-4 -top-4 w-20 h-20 rounded-full flex items-end justify-end pb-2 pr-2 text-white font-bold text-lg"
                style={{ backgroundColor: step.bgColor }}
              >
                {step.number}
              </div>
              <div className="flex flex-col h-full">
                <div 
                  className="w-16 h-16 rounded-full flex items-center justify-center mb-6"
                  style={{ backgroundColor: step.bgColor }}
                >
                  {step.icon}
                </div>
                <h3 className="text-xl font-bold mb-4">{step.title}</h3>
                <p className="text-gray-600 mb-6 flex-grow">{step.description}</p>
                <div className="w-12 h-1 group-hover:w-24 transition-all duration-300" style={{ backgroundColor: step.bgColor }}></div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div 
          className="bg-white p-8 rounded-xl shadow-lg max-w-3xl mx-auto text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h3 className="text-2xl font-bold mb-4">
            Pronto para migrar para uma solução mais <span className="text-[#2ECC71]">econômica?</span>
          </h3>
          <p className="text-gray-600 mb-6">
            Descubra qual solução parceira é ideal para o seu perfil e comece a economizar sem investimento inicial.
          </p>
          <div className="flex flex-col md:flex-row justify-center gap-4">
            <a 
              href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-[#2ECC71] hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
            >
              Para residências e pequenos negócios <ArrowRight className="h-5 w-5" />
            </a>
            <a 
              href="https://www.leadenergy.com.br/simular?ch=0f0547d3-7e73-442e-a915-34ef39efe74b&pr=a048c6c3-6224-43ab-ad95-7727cf36d2a2"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-[#FFC107] hover:bg-amber-500 text-white font-bold py-3 px-6 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
            >
              Para indústrias e grandes empresas <ArrowRight className="h-5 w-5" />
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorksSection;